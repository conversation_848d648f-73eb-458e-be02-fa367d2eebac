<?php

declare(strict_types=1);

namespace App\Tests\Functional\Infrastructure\Delivery\Api\V1\Organization;

use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Shared\Random\Generator;

final class DeleteOrganizationActionTest extends FunctionalTestCase
{
    protected const string ROUTE = '/tenants/v1/{_locale}/organizations/{id}';
    protected const string METHOD = 'DELETE';
    protected const string LOCALE = 'en';

    public function testDeleteOrganizationWorks(): void
    {
        // First create an organization to delete
        $createData = [
            'name' => 'Organization to Delete',
            'email' => '<EMAIL>',
            'phone' => '******-0127',
            'address' => '127 Delete Street'
        ];

        $createRoute = str_replace('{_locale}', self::LOCALE, '/tenants/v1/{_locale}/organizations');
        $this->client->jsonRequest('POST', $createRoute, $createData);
        $this->assertResponseStatusCodeSame(201);
        $createResponse = $this->getDecodedJsonResponse();
        $organizationId = $createResponse['data']['id'];

        // Now delete the organization
        $route = str_replace(['{_locale}', '{id}'], [self::LOCALE, $organizationId], self::ROUTE);
        $this->client->request(self::METHOD, $route);
        $this->assertResponseStatusCodeSame(204);

        $getRoute = str_replace(['{_locale}', '{id}'], [self::LOCALE, $organizationId], '/tenants/v1/{_locale}/organizations/{id}');
        $this->client->request('GET', $getRoute);
        $this->assertResponseStatusCodeSame(404);
    }

    public function testDeleteOrganizationFailsWithNotFound(): void
    {
        $nonExistentId = Generator::uuid();
        $route = str_replace(['{_locale}', '{id}'], [self::LOCALE, $nonExistentId], self::ROUTE);
        $this->client->request(self::METHOD, $route);
        $this->assertResponseStatusCodeSame(404);
    }

    public function testDeleteOrganizationTwice(): void
    {
        $createData = [
            'name' => 'Organization to Delete Twice',
            'email' => '<EMAIL>',
            'phone' => '******-0128',
            'address' => '128 Delete Street'
        ];

        $createRoute = str_replace('{_locale}', self::LOCALE, '/tenants/v1/{_locale}/organizations');
        $this->client->jsonRequest('POST', $createRoute, $createData);
        $this->assertResponseStatusCodeSame(201);
        $createResponse = $this->getDecodedJsonResponse();
        $organizationId = $createResponse['data']['id'];

        // Delete the organization first time
        $route = str_replace(['{_locale}', '{id}'], [self::LOCALE, $organizationId], self::ROUTE);
        $this->client->request(self::METHOD, $route);
        $this->assertResponseStatusCodeSame(204);

        // Try to delete the same organization again
        $this->client->request(self::METHOD, $route);
        $this->assertResponseStatusCodeSame(404);
    }

    public function testDeleteOrganizationWithInvalidId(): void
    {
        $invalidId = 'invalid-uuid';
        $route = str_replace(['{_locale}', '{id}'], [self::LOCALE, $invalidId], self::ROUTE);
        $this->client->request(self::METHOD, $route);

        // This might return 404 or 400 depending on how the routing handles invalid UUIDs
        $this->assertContains($this->client->getResponse()->getStatusCode(), [400, 404]);
    }
}
